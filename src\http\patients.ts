/* eslint-disable @typescript-eslint/no-explicit-any */
import { supabase } from "@/integrations/supabase/client";

export interface Patient {
  id: string;
  name: string;
  cpf: string;
  phone: string;
  email: string;
  mainComplaints: string;
  anamnesis: string;
  consultationSummaries: ConsultationSummary[];
  createdAt: Date;
  updatedAt: Date;
  userId: string;
}

export interface ConsultationSummary {
  id: string;
  date: Date;
  summary: string;
  patientId: string;
}

export type CreatePatientData = Omit<
  Patient,
  "id" | "createdAt" | "updatedAt" | "userId" | "consultationSummaries"
>;

export interface PatientCreationResult {
  patient: Patient;
  credentials?: {
    email: string;
    password: string;
  };
}

export type UpdatePatientData = Partial<CreatePatientData>;

export type CreateConsultationData = Omit<
  ConsultationSummary,
  "id" | "patientId"
>;

export type UpdateConsultationData = Partial<
  Omit<ConsultationSummary, "id" | "patientId">
>;

/**
 * Fetch all patients for a specific user with their consultation summaries
 */
export async function getPatients(userId: string): Promise<Patient[]> {
  const { data: patientsData, error: patientsError } = await supabase
    .from("patients")
    .select("*")
    .eq("user_id", userId)
    .order("created_at", { ascending: false });

  if (patientsError) throw patientsError;

  const patientIds = patientsData?.map((p) => p.id) || [];

  if (patientIds.length === 0) {
    return [];
  }

  const { data: consultationsData, error: consultationsError } = await supabase
    .from("consultation_summaries")
    .select("*")
    .in("patient_id", patientIds)
    .order("date", { ascending: false });

  if (consultationsError) throw consultationsError;

  const patientsWithConsultations =
    patientsData?.map((patient) => ({
      id: patient.id,
      name: patient.name,
      cpf: patient.cpf,
      phone: patient.phone,
      email: patient.email || "",
      mainComplaints: patient.main_complaints || "",
      anamnesis: patient.anamnesis || "",
      userId: patient.user_id,
      createdAt: new Date(patient.created_at),
      updatedAt: new Date(patient.updated_at),
      consultationSummaries:
        consultationsData
          ?.filter((consultation) => consultation.patient_id === patient.id)
          .map((consultation) => ({
            id: consultation.id,
            date: new Date(consultation.date),
            summary: consultation.summary,
            patientId: consultation.patient_id,
          })) || [],
    })) || [];

  return patientsWithConsultations;
}

// Generate a secure random password
function generateSecurePassword(): string {
  const length = 12;
  const charset =
    "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ**********!@#$%^&*";
  let password = "";

  // Ensure at least one of each character type
  password += "ABCDEFGHIJKLMNOPQRSTUVWXYZ"[Math.floor(Math.random() * 26)]; // Uppercase
  password += "abcdefghijklmnopqrstuvwxyz"[Math.floor(Math.random() * 26)]; // Lowercase
  password += "**********"[Math.floor(Math.random() * 10)]; // Number
  password += "!@#$%^&*"[Math.floor(Math.random() * 8)]; // Special

  // Fill remaining length
  for (let i = password.length; i < length; i++) {
    password += charset[Math.floor(Math.random() * charset.length)];
  }

  // Shuffle the password
  return password
    .split("")
    .sort(() => Math.random() - 0.5)
    .join("");
}

/**
 * Create a new patient with authentication credentials
 */
export async function createPatient(
  userId: string,
  patientData: CreatePatientData
): Promise<PatientCreationResult> {
  // Generate credentials for the patient
  const generatedPassword = generateSecurePassword();

  // Create auth user for the patient
  const { data: authData, error: authError } = await supabase.auth.signUp({
    email: patientData.email,
    password: generatedPassword,
    options: {
      emailRedirectTo: `${window.location.origin}/patient/questionnaires`,
    },
  });

  if (authError) throw authError;

  if (!authData.user) {
    throw new Error("Failed to create patient auth user");
  }

  // Create patient record linked to the auth user
  const { data, error } = await supabase
    .from("patients")
    .insert({
      user_id: authData.user.id, // Link to the patient's auth user, not the psychologist
      name: patientData.name,
      cpf: patientData.cpf,
      phone: patientData.phone,
      email: patientData.email,
      main_complaints: patientData.mainComplaints,
      anamnesis: patientData.anamnesis,
    })
    .select()
    .single();

  if (error) throw error;

  // Update the patient's profile to have 'patient' role
  const { error: profileError } = await supabase
    .from("profiles")
    .update({ role: "patient" })
    .eq("user_id", authData.user.id);

  if (profileError) {
    console.error("Error updating patient profile role:", profileError);
    // Don't throw error here as the patient was created successfully
    // The role can be updated later if needed
  }

  const patient: Patient = {
    id: data.id,
    name: data.name,
    cpf: data.cpf,
    phone: data.phone,
    email: data.email,
    mainComplaints: data.main_complaints || "",
    anamnesis: data.anamnesis || "",
    userId: data.user_id,
    createdAt: new Date(data.created_at),
    updatedAt: new Date(data.updated_at),
    consultationSummaries: [],
  };

  return {
    patient,
    credentials: {
      email: patientData.email,
      password: generatedPassword,
    },
  };
}

/**
 * Update an existing patient
 */
export async function updatePatient(
  patientId: string,
  updates: UpdatePatientData
): Promise<void> {
  const { error } = await supabase
    .from("patients")
    .update({
      name: updates.name,
      cpf: updates.cpf,
      phone: updates.phone,
      email: updates.email,
      main_complaints: updates.mainComplaints,
      anamnesis: updates.anamnesis,
    })
    .eq("id", patientId);

  if (error) throw error;
}

/**
 * Delete a patient
 */
export async function deletePatient(patientId: string): Promise<void> {
  const { error } = await supabase
    .from("patients")
    .delete()
    .eq("id", patientId);

  if (error) throw error;
}

/**
 * Create a new consultation summary for a patient
 */
export async function createConsultation(
  patientId: string,
  consultationData: CreateConsultationData
): Promise<ConsultationSummary> {
  const { data, error } = await supabase
    .from("consultation_summaries")
    .insert({
      patient_id: patientId,
      date: consultationData.date.toISOString().split("T")[0],
      summary: consultationData.summary,
    })
    .select()
    .single();

  if (error) throw error;

  return {
    id: data.id,
    date: new Date(data.date),
    summary: data.summary,
    patientId: data.patient_id,
  };
}

/**
 * Update an existing consultation summary
 */
export async function updateConsultation(
  consultationId: string,
  updates: UpdateConsultationData
): Promise<void> {
  const updateData: any = {};

  if (updates.date) {
    updateData.date = updates.date.toISOString().split("T")[0];
  }
  if (updates.summary !== undefined) {
    updateData.summary = updates.summary;
  }

  const { error } = await supabase
    .from("consultation_summaries")
    .update(updateData)
    .eq("id", consultationId);

  if (error) throw error;
}

/**
 * Delete a consultation summary
 */
export async function deleteConsultation(
  consultationId: string
): Promise<void> {
  const { error } = await supabase
    .from("consultation_summaries")
    .delete()
    .eq("id", consultationId);

  if (error) throw error;
}
