import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft, ArrowRight, Check, CalendarPlus } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { PatientFormSimple } from "@/components/patients/PatientFormSimple";
import { usePatients, Patient } from "@/context/PatientsContext";
import { useAppointments } from "@/context/AppointmentsContext";
import { AppointmentForm } from "@/components/appointments/AppointmentForm";
import { useToast } from "@/hooks/use-toast";

type Step = 1 | 2 | 3;

interface NewPatientData {
  name: string;
  cpf: string;
  phone: string;
  email: string;
  mainComplaints: string;
  anamnesis: string;
}

interface AppointmentData {
  title: string;
  description: string;
  appointmentDate: Date;
  startTime: string;
  endTime: string;
}

const NovaConsulta = () => {
  const [currentStep, setCurrentStep] = useState<Step>(1);
  const [newPatient, setNewPatient] = useState<NewPatientData | null>(null);
  const [selectedPatient, setSelectedPatient] = useState<Patient | null>(null);
  const [appointmentData, setAppointmentData] = useState<AppointmentData | null>(null);
  const [isCreatingPatient, setIsCreatingPatient] = useState(false);
  
  const { patients, addPatient } = usePatients();
  const { addAppointment } = useAppointments();
  const { toast } = useToast();
  const navigate = useNavigate();

  const steps = [
    { number: 1, title: "Paciente", description: "Selecionar ou cadastrar paciente" },
    { number: 2, title: "Agendamento", description: "Definir data e horário" },
    { number: 3, title: "Confirmação", description: "Revisar informações" },
  ];

  const handlePatientSubmit = async (patientData: NewPatientData) => {
    if (isCreatingPatient) {
      setNewPatient(patientData);
      await addPatient(patientData);
    }
    setCurrentStep(2);
  };

  const handleAppointmentSubmit = (data: AppointmentData) => {
    setAppointmentData(data);
    setCurrentStep(3);
  };

  const handleConfirm = async () => {
    if (!appointmentData) return;

    try {
      let patientId: string;
      
      if (selectedPatient) {
        patientId = selectedPatient.id;
      } else if (newPatient) {
        // Find the patient that was just created
        const createdPatient = patients.find(p => p.cpf === newPatient.cpf);
        if (!createdPatient) {
          toast({
            title: "Erro",
            description: "Paciente não encontrado",
            variant: "destructive",
          });
          return;
        }
        patientId = createdPatient.id;
      } else {
        return;
      }

      await addAppointment({
        title: appointmentData.title,
        description: appointmentData.description,
        appointment_date: appointmentData.appointmentDate.toISOString().split('T')[0],
        start_time: appointmentData.startTime,
        end_time: appointmentData.endTime,
        patient_id: patientId,
      });

      toast({
        title: "Sucesso",
        description: "Consulta agendada com sucesso!",
      });

      navigate("/agenda");
    } catch (error) {
      toast({
        title: "Erro",
        description: "Erro ao agendar consulta",
        variant: "destructive",
      });
    }
  };

  const renderStepIndicator = () => (
    <div className="flex justify-center mb-8">
      <div className="flex items-center space-x-4">
        {steps.map((step, index) => (
          <React.Fragment key={step.number}>
            <div className="flex flex-col items-center">
              <div
                className={`w-10 h-10 rounded-full flex items-center justify-center border-2 ${
                  currentStep >= step.number
                    ? "bg-primary border-primary text-primary-foreground"
                    : "border-muted-foreground text-muted-foreground"
                }`}
              >
                {currentStep > step.number ? (
                  <Check className="w-5 h-5" />
                ) : (
                  step.number
                )}
              </div>
              <div className="mt-2 text-center">
                <p className="text-sm font-medium">{step.title}</p>
                <p className="text-xs text-muted-foreground">{step.description}</p>
              </div>
            </div>
            {index < steps.length - 1 && (
              <div
                className={`w-16 h-0.5 ${
                  currentStep > step.number ? "bg-primary" : "bg-muted"
                }`}
              />
            )}
          </React.Fragment>
        ))}
      </div>
    </div>
  );

  const renderStep1 = () => (
    <Card>
      <CardHeader>
        <CardTitle>Selecionar Paciente</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <Button
            variant={!isCreatingPatient ? "default" : "outline"}
            onClick={() => setIsCreatingPatient(false)}
            className="h-20 flex flex-col"
          >
            <span className="font-medium">Paciente Existente</span>
            <span className="text-sm opacity-80">Selecionar da lista</span>
          </Button>
          <Button
            variant={isCreatingPatient ? "default" : "outline"}
            onClick={() => setIsCreatingPatient(true)}
            className="h-20 flex flex-col"
          >
            <span className="font-medium">Novo Paciente</span>
            <span className="text-sm opacity-80">Cadastrar novo</span>
          </Button>
        </div>

        {!isCreatingPatient ? (
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Pacientes Cadastrados</h3>
            <div className="grid gap-3 max-h-96 overflow-y-auto">
              {patients.map((patient) => (
                <Card
                  key={patient.id}
                  className={`cursor-pointer transition-colors ${
                    selectedPatient?.id === patient.id
                      ? "border-primary bg-primary/5"
                      : "hover:bg-muted/50"
                  }`}
                  onClick={() => setSelectedPatient(patient)}
                >
                  <CardContent className="p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <h4 className="font-medium">{patient.name}</h4>
                        <p className="text-sm text-muted-foreground">{patient.phone}</p>
                      </div>
                      {selectedPatient?.id === patient.id && (
                        <Badge variant="default">Selecionado</Badge>
                      )}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
            <div className="flex justify-end">
              <Button
                onClick={() => setCurrentStep(2)}
                disabled={!selectedPatient}
              >
                Próximo
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </div>
          </div>
        ) : (
          <PatientFormSimple onSubmit={handlePatientSubmit} />
        )}
      </CardContent>
    </Card>
  );

  const renderStep2 = () => (
    <Card>
      <CardHeader>
        <CardTitle>Dados do Agendamento</CardTitle>
      </CardHeader>
      <CardContent>
        <AppointmentForm onSubmit={handleAppointmentSubmit} />
        <div className="flex justify-between mt-6">
          <Button variant="outline" onClick={() => setCurrentStep(1)}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Anterior
          </Button>
        </div>
      </CardContent>
    </Card>
  );

  const renderStep3 = () => (
    <Card>
      <CardHeader>
        <CardTitle>Confirmação</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-medium mb-3">Paciente</h3>
            <Card className="bg-muted/50">
              <CardContent className="p-4">
                {selectedPatient ? (
                  <>
                    <p className="font-medium">{selectedPatient.name}</p>
                    <p className="text-sm text-muted-foreground">{selectedPatient.phone}</p>
                  </>
                ) : newPatient ? (
                  <>
                    <p className="font-medium">{newPatient.name}</p>
                    <p className="text-sm text-muted-foreground">{newPatient.phone}</p>
                  </>
                ) : null}
              </CardContent>
            </Card>
          </div>

          {appointmentData && (
            <div>
              <h3 className="text-lg font-medium mb-3">Consulta</h3>
              <Card className="bg-muted/50">
                <CardContent className="p-4 space-y-2">
                  <p><span className="font-medium">Título:</span> {appointmentData.title}</p>
                  <p><span className="font-medium">Data:</span> {appointmentData.appointmentDate.toLocaleDateString('pt-BR')}</p>
                  <p><span className="font-medium">Horário:</span> {appointmentData.startTime} - {appointmentData.endTime}</p>
                  {appointmentData.description && (
                    <p><span className="font-medium">Descrição:</span> {appointmentData.description}</p>
                  )}
                </CardContent>
              </Card>
            </div>
          )}
        </div>

        <div className="flex justify-between">
          <Button variant="outline" onClick={() => setCurrentStep(2)}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Anterior
          </Button>
          <Button onClick={handleConfirm}>
            <Check className="w-4 h-4 mr-2" />
            Confirmar Consulta
          </Button>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-foreground flex items-center">
              <CalendarPlus className="w-8 h-8 mr-3 text-primary" />
              Nova Consulta
            </h1>
            <p className="text-muted-foreground">
              Agende uma nova consulta em 3 passos simples
            </p>
          </div>
          <Button variant="outline" onClick={() => navigate(-1)}>
            <ArrowLeft className="w-4 h-4 mr-2" />
            Voltar
          </Button>
        </div>
      </div>

      {renderStepIndicator()}

      <div className="max-w-4xl mx-auto">
        {currentStep === 1 && renderStep1()}
        {currentStep === 2 && renderStep2()}
        {currentStep === 3 && renderStep3()}
      </div>
    </div>
  );
};

export default NovaConsulta;