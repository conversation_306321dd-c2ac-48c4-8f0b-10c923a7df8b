import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { PatientsProvider } from "@/context/PatientsContext";
import { AppointmentsProvider } from "@/context/AppointmentsContext";
import { QuestionnairesProvider } from "@/context/QuestionnairesContext";
import { AuthProvider } from "@/context/AuthContext";
import AppLayout from "@/components/layout/AppLayout";
import Dashboard from "@/pages/Dashboard";
import Patients from "@/pages/Patients";
import Login from "@/pages/Login";
import Agenda from "@/pages/agenda";
import Settings from "@/pages/Settings";
import { Questionnaires } from "@/pages/Questionnaires";
import PatientQuestionnaires from "@/pages/PatientQuestionnaires";
import NovaConsulta from "@/pages/NovaConsulta";
import ProtectedRoute from "@/components/ProtectedRoute";
import RoleBasedRedirect from "@/components/RoleBasedRedirect";
import NotFound from "@/pages/NotFound";
import { Toaster } from "@/components/ui/toaster";

const queryClient = new QueryClient();

const App = () => (
  <QueryClientProvider client={queryClient}>
    <AuthProvider>
      <PatientsProvider>
        <AppointmentsProvider>
          <QuestionnairesProvider>
            <TooltipProvider>
              <BrowserRouter>
                <Routes>
                  <Route path="/login" element={<Login />} />
                  <Route
                    path="/"
                    element={
                      <ProtectedRoute>
                        <AppLayout />
                      </ProtectedRoute>
                    }
                  >
                    {/* Role-based redirect for root path */}
                    <Route index element={<RoleBasedRedirect />} />

                    {/* Doctor-only routes */}
                    <Route
                      path="dashboard"
                      element={
                        <ProtectedRoute requireRole="doctor">
                          <Dashboard />
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="nova-consulta"
                      element={
                        <ProtectedRoute requireRole="doctor">
                          <NovaConsulta />
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="pacientes"
                      element={
                        <ProtectedRoute requireRole="doctor">
                          <Patients />
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="agenda"
                      element={
                        <ProtectedRoute requireRole="doctor">
                          <Agenda />
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="questionarios"
                      element={
                        <ProtectedRoute requireRole="doctor">
                          <Questionnaires />
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="relatorios"
                      element={
                        <ProtectedRoute requireRole="doctor">
                          <div className="p-8 text-center">
                            <h2 className="text-2xl">
                              Relatórios em desenvolvimento
                            </h2>
                          </div>
                        </ProtectedRoute>
                      }
                    />
                    <Route
                      path="configuracoes"
                      element={
                        <ProtectedRoute requireRole="doctor">
                          <Settings />
                        </ProtectedRoute>
                      }
                    />

                    {/* Patient-only routes */}
                    <Route
                      path="patient/questionnaires"
                      element={
                        <ProtectedRoute requireRole="patient">
                          <PatientQuestionnaires />
                        </ProtectedRoute>
                      }
                    />

                    {/* Shared routes (both roles can access) */}
                    <Route
                      path="perfil"
                      element={
                        <div className="p-8 text-center">
                          <h2 className="text-2xl">
                            Perfil em desenvolvimento
                          </h2>
                        </div>
                      }
                    />
                  </Route>
                  {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
                  <Route path="*" element={<NotFound />} />
                </Routes>
                <Toaster />
                <Sonner />
              </BrowserRouter>
            </TooltipProvider>
          </QuestionnairesProvider>
        </AppointmentsProvider>
      </PatientsProvider>
    </AuthProvider>
  </QueryClientProvider>
);

export default App;
