import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Card, CardContent } from "@/components/ui/card";
import { Check, Copy, Eye, EyeOff } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface CredentialsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  email: string;
  password: string;
  patientName: string;
}

export function CredentialsDialog({ 
  open, 
  onOpenChange, 
  email, 
  password, 
  patientName 
}: CredentialsDialogProps) {
  const [showPassword, setShowPassword] = useState(false);
  const [copiedEmail, setCopiedEmail] = useState(false);
  const [copiedPassword, setCopiedPassword] = useState(false);
  const { toast } = useToast();

  const copyToClipboard = async (text: string, type: 'email' | 'password') => {
    try {
      await navigator.clipboard.writeText(text);
      if (type === 'email') {
        setCopiedEmail(true);
        setTimeout(() => setCopiedEmail(false), 2000);
      } else {
        setCopiedPassword(true);
        setTimeout(() => setCopiedPassword(false), 2000);
      }
      toast({
        title: "Copiado!",
        description: `${type === 'email' ? 'E-mail' : 'Senha'} copiado para a área de transferência.`,
      });
    } catch (err) {
      toast({
        title: "Erro",
        description: "Não foi possível copiar para a área de transferência.",
        variant: "destructive",
      });
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Credenciais do Paciente Criadas</DialogTitle>
          <DialogDescription>
            Credenciais de acesso criadas para <strong>{patientName}</strong>. 
            Compartilhe essas informações com o paciente de forma segura.
          </DialogDescription>
        </DialogHeader>
        
        <Card className="border-2 border-primary/20 bg-primary/5">
          <CardContent className="pt-6 space-y-4">
            <div className="space-y-2">
              <Label htmlFor="email" className="text-sm font-medium">
                E-mail de acesso:
              </Label>
              <div className="flex items-center space-x-2">
                <Input
                  id="email"
                  value={email}
                  readOnly
                  className="bg-background"
                />
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(email, 'email')}
                  className="px-3"
                >
                  {copiedEmail ? (
                    <Check className="h-4 w-4 text-green-600" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="password" className="text-sm font-medium">
                Senha temporária:
              </Label>
              <div className="flex items-center space-x-2">
                <div className="relative flex-1">
                  <Input
                    id="password"
                    type={showPassword ? "text" : "password"}
                    value={password}
                    readOnly
                    className="bg-background pr-10"
                  />
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-0 top-0 h-full px-3"
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => copyToClipboard(password, 'password')}
                  className="px-3"
                >
                  {copiedPassword ? (
                    <Check className="h-4 w-4 text-green-600" />
                  ) : (
                    <Copy className="h-4 w-4" />
                  )}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="text-sm text-muted-foreground space-y-2">
          <p className="font-medium">Instruções importantes:</p>
          <ul className="list-disc list-inside space-y-1 text-xs">
            <li>Compartilhe essas credenciais com o paciente de forma segura</li>
            <li>O paciente deve acessar o sistema em: <strong>/patient/questionnaires</strong></li>
            <li>Recomende que o paciente altere a senha no primeiro acesso</li>
            <li>Estas informações não serão exibidas novamente</li>
          </ul>
        </div>

        <DialogFooter>
          <Button onClick={() => onOpenChange(false)} className="w-full">
            Entendi, fechar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}