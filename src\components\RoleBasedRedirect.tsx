import { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/context/AuthContext';

/**
 * Component that handles role-based redirects after authentication
 * This component should be used in routes that need to redirect users
 * based on their role (e.g., the root "/" route)
 */
const RoleBasedRedirect: React.FC = () => {
  const { role, loading, isAuthenticated } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (!loading && isAuthenticated && role) {
      if (role === 'patient') {
        navigate('/patient/questionnaires', { replace: true });
      } else if (role === 'doctor') {
        navigate('/dashboard', { replace: true });
      }
    }
  }, [role, loading, isAuthenticated, navigate]);

  // Show loading while determining role
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  // This component doesn't render anything visible
  // It just handles the redirect logic
  return null;
};

export default RoleBasedRedirect;
