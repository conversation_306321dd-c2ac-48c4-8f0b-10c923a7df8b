import { useState, useEffect } from "react";
import { usePatients, Patient } from "@/context/PatientsContext";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { ArrowLeft, Save } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { CredentialsDialog } from "@/components/ui/dialog-credentials";

interface PatientFormProps {
  patientId?: string | null;
  onClose: () => void;
}

export function PatientForm({ patientId, onClose }: PatientFormProps) {
  const { patients, addPatient, updatePatient } = usePatients();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const [showCredentials, setShowCredentials] = useState(false);
  const [generatedCredentials, setGeneratedCredentials] = useState<{
    email: string;
    password: string;
  } | null>(null);

  const isEditing = !!patientId;
  const patient = isEditing ? patients.find(p => p.id === patientId) : null;

  const [formData, setFormData] = useState({
    name: "",
    cpf: "",
    phone: "",
    email: "",
    mainComplaints: "",
    anamnesis: "",
  });

  useEffect(() => {
    if (patient) {
      setFormData({
        name: patient.name,
        cpf: patient.cpf,
        phone: patient.phone,
        email: patient.email || "",
        mainComplaints: patient.mainComplaints,
        anamnesis: patient.anamnesis,
      });
    }
  }, [patient]);

  const formatCPF = (value: string) => {
    const numbers = value.replace(/\D/g, '');
    return numbers.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4');
  };

  const formatPhone = (value: string) => {
    const numbers = value.replace(/\D/g, '');
    if (numbers.length <= 10) {
      return numbers.replace(/(\d{2})(\d{4})(\d{4})/, '($1) $2-$3');
    }
    return numbers.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
  };

  const handleInputChange = (field: string, value: string) => {
    let formattedValue = value;
    
    if (field === 'cpf') {
      formattedValue = formatCPF(value);
    } else if (field === 'phone') {
      formattedValue = formatPhone(value);
    }

    setFormData(prev => ({
      ...prev,
      [field]: formattedValue
    }));
  };

  const validateForm = () => {
    if (!formData.name.trim()) {
      toast({
        title: "Erro de validação",
        description: "Nome é obrigatório",
        variant: "destructive",
      });
      return false;
    }

    if (!formData.cpf.trim()) {
      toast({
        title: "Erro de validação", 
        description: "CPF é obrigatório",
        variant: "destructive",
      });
      return false;
    }

    if (!formData.phone.trim()) {
      toast({
        title: "Erro de validação",
        description: "Telefone é obrigatório", 
        variant: "destructive",
      });
      return false;
    }

    if (!isEditing && !formData.email.trim()) {
      toast({
        title: "Erro de validação",
        description: "E-mail é obrigatório para novos pacientes",
        variant: "destructive",
      });
      return false;
    }

    if (!isEditing && formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
      toast({
        title: "Erro de validação",
        description: "E-mail deve ter um formato válido",
        variant: "destructive",
      });
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;

    setIsLoading(true);

    try {
      if (isEditing && patientId) {
        await updatePatient(patientId, formData);
        onClose();
      } else {
        const result = await addPatient(formData);
        if (result && 'credentials' in result && result.credentials) {
          setGeneratedCredentials(result.credentials);
          setShowCredentials(true);
        } else {
          toast({
            title: "Paciente cadastrado",
            description: "Novo paciente foi adicionado com sucesso.",
          });
          onClose();
        }
      }
    } catch (error) {
      toast({
        title: "Erro",
        description: "Ocorreu um erro ao salvar os dados.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center space-x-4">
        <Button variant="ghost" onClick={onClose}>
          <ArrowLeft className="w-4 h-4 mr-2" />
          Voltar
        </Button>
        <div>
          <h1 className="text-3xl font-bold text-foreground">
            {isEditing ? "Editar Paciente" : "Novo Paciente"}
          </h1>
          <p className="text-muted-foreground">
            {isEditing ? "Atualize os dados do paciente" : "Cadastre um novo paciente"}
          </p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Dados Pessoais</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="name">Nome Completo *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder="Digite o nome completo"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="cpf">CPF *</Label>
                <Input
                  id="cpf"
                  value={formData.cpf}
                  onChange={(e) => handleInputChange('cpf', e.target.value)}
                  placeholder="000.000.000-00"
                  maxLength={14}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="phone">Telefone *</Label>
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => handleInputChange('phone', e.target.value)}
                  placeholder="(00) 00000-0000"
                  maxLength={15}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="email">
                  E-mail {!isEditing && "*"}
                </Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  placeholder="<EMAIL>"
                  required={!isEditing}
                />
                {!isEditing && (
                  <p className="text-sm text-muted-foreground">
                    Será criada uma conta de acesso para o paciente
                  </p>
                )}
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="mainComplaints">Queixas Principais</Label>
              <Textarea
                id="mainComplaints"
                value={formData.mainComplaints}
                onChange={(e) => handleInputChange('mainComplaints', e.target.value)}
                placeholder="Descreva as principais queixas e sintomas relatados pelo paciente..."
                className="min-h-[120px]"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="anamnesis">Anamnese</Label>
              <Textarea
                id="anamnesis"
                value={formData.anamnesis}
                onChange={(e) => handleInputChange('anamnesis', e.target.value)}
                placeholder="Histórico médico, familiar, medicamentos em uso, informações relevantes..."
                className="min-h-[150px]"
              />
            </div>

            <div className="flex justify-end space-x-4 pt-6 border-t">
              <Button type="button" variant="outline" onClick={onClose}>
                Cancelar
              </Button>
              <Button 
                type="submit"
                disabled={isLoading}
                className="bg-primary hover:bg-primary-hover"
              >
                <Save className="w-4 h-4 mr-2" />
                {isLoading ? "Salvando..." : "Salvar"}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>

      {generatedCredentials && (
        <CredentialsDialog
          open={showCredentials}
          onOpenChange={(open) => {
            setShowCredentials(open);
            if (!open) {
              onClose();
            }
          }}
          email={generatedCredentials.email}
          password={generatedCredentials.password}
          patientName={formData.name}
        />
      )}
    </div>
  );
}