-- Enable RLS on _prisma_migrations table (if safe to do so)
-- Note: This table is used by Prisma for migration tracking, but since it's in public schema
-- we need to enable RLS for security compliance
ALTER TABLE public._prisma_migrations ENABLE ROW LEVEL SECURITY;

-- Create policy to restrict access to this table
-- Only authenticated users should be able to read migration history
CREATE POLICY "Only authenticated users can view migration history" 
ON public._prisma_migrations 
FOR SELECT 
TO authenticated 
USING (true);