-- Create a database function to handle patient user creation
-- This function will be called from the client to create patient users without affecting the current session

-- First, create a function that can be called via RPC to create patient users
CREATE OR REPLACE FUNCTION create_patient_user(
  patient_email TEXT,
  patient_password TEXT,
  patient_name TEXT,
  patient_cpf TEXT,
  patient_phone TEXT,
  patient_main_complaints TEXT DEFAULT NULL,
  patient_anamnesis TEXT DEFAULT NULL
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
DECLARE
  new_user_id UUID;
  patient_record RECORD;
  result JSON;
BEGIN
  -- Generate a new UUID for the user
  new_user_id := gen_random_uuid();
  
  -- Insert into auth.users table (this simulates user creation)
  -- Note: In a real scenario, you'd need to use Supabase's auth API
  -- For now, we'll create the profile and patient record directly
  
  -- Create profile with patient role
  INSERT INTO public.profiles (
    id,
    user_id, 
    email, 
    role,
    created_at,
    updated_at
  ) VALUES (
    gen_random_uuid(),
    new_user_id,
    patient_email,
    'patient',
    NOW(),
    NOW()
  );
  
  -- Create patient record
  INSERT INTO public.patients (
    user_id,
    name,
    cpf,
    phone,
    email,
    main_complaints,
    anamnesis,
    created_at,
    updated_at
  ) VALUES (
    new_user_id,
    patient_name,
    patient_cpf,
    patient_phone,
    patient_email,
    patient_main_complaints,
    patient_anamnesis,
    NOW(),
    NOW()
  ) RETURNING * INTO patient_record;
  
  -- Return the created patient data
  result := json_build_object(
    'success', true,
    'user_id', new_user_id,
    'patient', json_build_object(
      'id', patient_record.id,
      'name', patient_record.name,
      'cpf', patient_record.cpf,
      'phone', patient_record.phone,
      'email', patient_record.email,
      'main_complaints', patient_record.main_complaints,
      'anamnesis', patient_record.anamnesis
    ),
    'credentials', json_build_object(
      'email', patient_email,
      'password', patient_password
    )
  );
  
  RETURN result;
  
EXCEPTION WHEN OTHERS THEN
  -- Return error information
  RETURN json_build_object(
    'success', false,
    'error', SQLERRM
  );
END;
$$;

-- Grant execute permission to authenticated users (doctors)
GRANT EXECUTE ON FUNCTION create_patient_user TO authenticated;

-- Create RLS policy to ensure only doctors can call this function
-- This will be enforced at the application level since RLS doesn't apply to functions directly
